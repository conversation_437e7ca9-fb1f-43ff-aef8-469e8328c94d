package com.sq.dlyz_flutter.pay

import com.sq.dlyz_flutter.pay.strategies.AlipayStrategy

/**
 * 支付策略工厂类
 * 负责创建和管理支付策略实例
 */
object PaymentFactory {
    
    /**
     * 创建支付策略
     */
    fun createStrategy(paymentType: PaymentType): IPayStrategy? {
        return when (paymentType) {
            PaymentType.ALIPAY -> AlipayStrategy()
            else -> null
        }
    }
    
    /**
     * 创建所有支持的支付策略
     */
    fun createAllSupportedStrategies(): List<IPayStrategy> {
        return listOf(
            AlipayStrategy()
        )
    }
    
    /**
     * 获取所有支持的支付类型
     */
    fun getSupportedPaymentTypes(): List<PaymentType> {
        return listOf(
            PaymentType.ALIPAY
        )
    }
    
    /**
     * 检查支付类型是否支持
     */
    fun isPaymentTypeSupported(paymentType: PaymentType): Boolean {
        return getSupportedPaymentTypes().contains(paymentType)
    }
}
