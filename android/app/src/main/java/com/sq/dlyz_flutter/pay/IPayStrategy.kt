package com.sq.dlyz_flutter.pay

import android.app.Activity
import android.content.Context

/**
 * 支付策略接口
 * 定义所有支付方式的通用行为
 */
interface IPayStrategy {
    
    /**
     * 获取支付方式名称
     */
    fun getPaymentName(): String
    
    /**
     * 获取支付方式类型
     */
    fun getPaymentType(): PaymentType
    
    /**
     * 执行支付
     * @param activity 当前Activity
     * @param payRequest 支付请求参数
     * @param callback 支付回调
     */
    fun pay(activity: Activity, payRequest: PayRequest, callback: PayCallback)
    
    /**
     * 是否支持当前环境
     * @param context 上下文
     * @return true表示支持，false表示不支持
     */
    fun isSupported(context: Context): Boolean
    
    /**
     * 释放资源
     */
    fun release()
}
