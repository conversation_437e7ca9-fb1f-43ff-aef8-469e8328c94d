package com.sq.dlyz_flutter.pay.strategies

import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import com.alipay.sdk.app.PayTask
import com.sq.dlyz_flutter.pay.*
import java.util.concurrent.Executors

/**
 * 支付宝支付策略实现
 */
class AlipayStrategy : PayStrategy {
    
    companion object {
        private const val TAG = "AlipayStrategy"
        private const val ALIPAY_PACKAGE_NAME = "com.eg.android.AlipayGphone"
    }
    
    private var appId: String = ""
    private var privateKey: String = ""
    private var isDebug: Boolean = false
    private val executor = Executors.newSingleThreadExecutor()
    private val mainHandler = Handler(Looper.getMainLooper())
    
    override fun getPaymentName(): String = "支付宝"
    
    override fun getPaymentType(): PaymentType = PaymentType.ALIPAY
    
    override fun initialize(context: Context, config: PayConfig) {
        this.appId = config.appId
        this.privateKey = config.privateKey
        this.isDebug = config.isDebug
        
        Log.d(TAG, "支付宝SDK初始化完成, AppId: $appId, Debug: $isDebug")
    }
    
    override fun pay(activity: Activity, payRequest: PayRequest, callback: PayCallback) {
        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(privateKey)) {
            callback.onPayFailed(
                PayResult(
                    status = PayStatus.INVALID_PARAMS,
                    orderId = payRequest.orderId,
                    message = "支付宝配置参数不完整"
                )
            )
            return
        }
        
        // 验证支付数据格式
        val paymentJson = payRequest.getPaymentDataAsJson()
        if (paymentJson == null) {
            callback.onPayFailed(
                PayResult(
                    status = PayStatus.INVALID_PARAMS,
                    orderId = payRequest.orderId,
                    message = "支付数据格式无效"
                )
            )
            return
        }
        
        // 在子线程中执行支付
        executor.execute {
            try {
                 val payResult = PayTask(activity).payV2(payRequest.paymentData, true)
                
                // 回到主线程执行回调
                mainHandler.post {
                    handlePayResult(payResult, payRequest, callback)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "支付宝支付异常", e)
                mainHandler.post {
                    callback.onPayFailed(
                        PayResult(
                            status = PayStatus.FAILED,
                            orderId = payRequest.orderId,
                            message = "支付异常: ${e.message}"
                        )
                    )
                }
            }
        }
    }

    override fun isSupported(context: Context): Boolean {
        return try {
            val packageManager = context.packageManager
            packageManager.getPackageInfo(ALIPAY_PACKAGE_NAME, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            Log.d(TAG, "支付宝应用未安装")
            false
        }
    }
    
    override fun release() {
        executor.shutdown()
        Log.d(TAG, "支付宝策略资源已释放")
    }
    
    /**
     * 处理支付结果
     */
    private fun handlePayResult(
        resultMap: Map<String, String>,
        payRequest: PayRequest,
        callback: PayCallback
    ) {
        val resultStatus = resultMap["resultStatus"] ?: ""
        val memo = resultMap["memo"] ?: ""
        
        when (resultStatus) {
            "9000" -> {
                // 支付成功
                callback.onPaySuccess(
                    PayResult(
                        status = PayStatus.SUCCESS,
                        orderId = payRequest.orderId,
                        transactionId = resultMap["trade_no"] ?: "",
                        message = "支付成功",
                        rawData = resultMap.toString()
                    )
                )
            }
            "8000" -> {
                // 正在处理中
                callback.onPayPending(
                    PayResult(
                        status = PayStatus.PENDING,
                        orderId = payRequest.orderId,
                        message = "支付结果确认中"
                    )
                )
            }
            "6001" -> {
                // 用户取消
                callback.onPayCancelled(
                    PayResult(
                        status = PayStatus.CANCELLED,
                        orderId = payRequest.orderId,
                        message = "用户取消支付"
                    )
                )
            }
            else -> {
                // 支付失败
                callback.onPayFailed(
                    PayResult(
                        status = PayStatus.FAILED,
                        orderId = payRequest.orderId,
                        message = memo.ifEmpty { "支付失败" }
                    )
                )
            }
        }
    }
}
