package com.sq.dlyz_flutter.pay

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*

/**
 * 支付工具类
 * 提供支付相关的通用工具方法
 */
object PaymentUtils {
    
    private const val TAG = "PaymentUtils"
    
    /**
     * 生成时间戳字符串
     */
    fun generateTimestamp(): String {
        return (System.currentTimeMillis() / 1000).toString()
    }
    
    /**
     * 生成随机字符串
     */
    fun generateRandomString(length: Int = 32): String {
        val chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return (1..length).map { chars.random() }.joinToString("")
    }
    
    /**
     * MD5加密
     */
    fun md5(input: String): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            val digest = md.digest(input.toByteArray())
            digest.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Log.e(TAG, "MD5加密失败", e)
            ""
        }
    }
    
    /**
     * 检查应用是否安装
     */
    fun isAppInstalled(context: Context, packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * 获取应用版本号
     */
    fun getAppVersionCode(context: Context, packageName: String): Int {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
            packageInfo.versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            -1
        }
    }
    
    /**
     * 格式化时间
     */
    fun formatTime(timestamp: Long, pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
        return try {
            val sdf = SimpleDateFormat(pattern, Locale.getDefault())
            sdf.format(Date(timestamp))
        } catch (e: Exception) {
            Log.e(TAG, "时间格式化失败", e)
            ""
        }
    }

    /**
     * 验证JSON字符串格式
     */
    fun isValidJson(jsonString: String): Boolean {
        return try {
            org.json.JSONObject(jsonString)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 创建默认支付配置
     */
    fun createDefaultPayConfig(
        appId: String,
        merchantId: String = "",
        privateKey: String = "",
        publicKey: String = "",
        isDebug: Boolean = false
    ): PayConfig {
        return PayConfig(
            appId = appId,
            merchantId = merchantId,
            privateKey = privateKey,
            publicKey = publicKey,
            isDebug = isDebug
        )
    }
    
    /**
     * 解析支付结果状态码
     */
    fun parsePayStatusFromCode(code: String): PayStatus {
        return when (code) {
            "0", "9000", "SUCCESS" -> PayStatus.SUCCESS
            "-1", "FAILED" -> PayStatus.FAILED
            "-2", "6001", "CANCELLED" -> PayStatus.CANCELLED
            "1", "8000", "PENDING" -> PayStatus.PENDING
            "-3", "TIMEOUT" -> PayStatus.TIMEOUT
            "-4", "NETWORK_ERROR" -> PayStatus.NETWORK_ERROR
            "-5", "INVALID_PARAMS" -> PayStatus.INVALID_PARAMS
            "-6", "NOT_INSTALLED" -> PayStatus.NOT_INSTALLED
            else -> PayStatus.UNKNOWN
        }
    }
}

/**
 * 支付参数验证结果
 */
data class PayValidationResult(
    val isValid: Boolean,
    val errors: List<String>
)
