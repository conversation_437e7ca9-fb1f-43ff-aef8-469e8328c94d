package com.sq.dlyz_flutter.pay

import android.app.Activity
import android.content.Context
import android.util.Log
import java.util.concurrent.ConcurrentHashMap

/**
 * 支付管理器 - 策略模式的上下文类
 * 负责管理所有支付策略，提供统一的支付接口
 */
class PaymentManager private constructor() {

    companion object {
        private const val TAG = "PaymentManager"

        @Volatile
        private var INSTANCE: PaymentManager? = null

        fun getInstance(): PaymentManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PaymentManager().also { INSTANCE = it }
            }
        }
    }

    // 存储所有注册的支付策略
    private val strategies = ConcurrentHashMap<PaymentType, PayStrategy>()


    fun init(context: Context) {
        val strategies = PaymentFactory.createAllSupportedStrategies()
        registerStrategies(*strategies.toTypedArray())
        val configs = mapOf(
            PaymentType.ALIPAY to PaymentUtils.createDefaultPayConfig(
                appId = "",
                privateKey = "",
                isDebug = true
            ),
        )
        initializeAll(context, configs)
    }

    /**
     * 初始化所有支付策略
     */
    private fun initializeAll(context: Context, configs: Map<PaymentType, PayConfig>) {
        strategies.forEach { (type, strategy) ->
            configs[type]?.let { config ->
                try {
                    strategy.initialize(context, config)
                    Log.d(TAG, "初始化支付策略成功: ${strategy.getPaymentName()}")
                } catch (e: Exception) {
                    Log.e(TAG, "初始化支付策略失败: ${strategy.getPaymentName()}", e)
                }
            }
        }
    }

    /**
     * 注册支付策略
     */
    private fun registerStrategy(strategy: PayStrategy) {
        strategies[strategy.getPaymentType()] = strategy
        Log.d(TAG, "注册支付策略: ${strategy.getPaymentName()}")
    }

    /**
     * 注册多个支付策略
     */
    private fun registerStrategies(vararg strategies: PayStrategy) {
        strategies.forEach { registerStrategy(it) }
    }

    /**
     * 获取支付策略
     */
    fun getStrategy(paymentType: PaymentType): PayStrategy? {
        return strategies[paymentType]
    }

    /**
     * 获取所有已注册的支付策略
     */
    fun getAllStrategies(): List<PayStrategy> {
        return strategies.values.toList()
    }

    /**
     * 获取所有支持的支付方式
     */
    fun getSupportedPaymentTypes(context: Context): List<PaymentType> {
        return strategies.values
            .filter { it.isSupported(context) }
            .map { it.getPaymentType() }
    }

    /**
     * 执行支付
     */
    fun pay(
        activity: Activity,
        paymentType: PaymentType,
        payRequest: PayRequest,
        callback: PayCallback
    ) {
        val strategy = strategies[paymentType]
        if (strategy == null) {
            Log.e(TAG, "未找到支付策略: $paymentType")
            callback.onPayFailed(
                PayResult(
                    status = PayStatus.FAILED,
                    orderId = payRequest.orderId,
                    message = "不支持的支付方式: ${paymentType.displayName}"
                )
            )
            return
        }

        if (!strategy.isSupported(activity)) {
            Log.e(TAG, "当前环境不支持支付方式: ${strategy.getPaymentName()}")
            callback.onPayFailed(
                PayResult(
                    status = PayStatus.NOT_INSTALLED,
                    orderId = payRequest.orderId,
                    message = "当前环境不支持${strategy.getPaymentName()}"
                )
            )
            return
        }

        try {
            Log.d(TAG, "开始执行支付: ${strategy.getPaymentName()}, 订单: ${payRequest.orderId}")
            strategy.pay(activity, payRequest, callback)
        } catch (e: Exception) {
            Log.e(TAG, "支付执行异常: ${strategy.getPaymentName()}", e)
            callback.onPayFailed(
                PayResult(
                    status = PayStatus.FAILED,
                    orderId = payRequest.orderId,
                    message = "支付执行异常: ${e.message}"
                )
            )
        }
    }

    /**
     * 释放所有资源
     */
    fun releaseAll() {
        strategies.values.forEach { strategy ->
            try {
                strategy.release()
                Log.d(TAG, "释放支付策略: ${strategy.getPaymentName()}")
            } catch (e: Exception) {
                Log.e(TAG, "释放支付策略异常: ${strategy.getPaymentName()}", e)
            }
        }
        strategies.clear()
    }
}
